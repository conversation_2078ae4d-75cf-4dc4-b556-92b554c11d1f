<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Forgot Password</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Forgot Password Functionality</h1>
        <p>This page helps test the forgot password functionality without reCAPTCHA.</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" name="email" required placeholder="Enter email address">
            </div>
            
            <button type="button" onclick="testForgotPassword()">Test Forgot Password (No reCAPTCHA)</button>
            <button type="button" onclick="testOriginalForgotPassword()">Test Original (With reCAPTCHA)</button>
        </form>
        
        <div id="result" class="result"></div>
        
        <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
            <h3>Instructions:</h3>
            <ol>
                <li>Enter a valid email address that exists in the users table</li>
                <li>Click "Test Forgot Password" to test without reCAPTCHA</li>
                <li>Check the Laravel logs at <code>storage/logs/laravel.log</code> for detailed debugging info</li>
                <li>Check your email inbox for the reset email</li>
            </ol>
            
            <h3>Common Test Emails:</h3>
            <ul>
                <li><EMAIL></li>
                <li><EMAIL></li>
                <li><EMAIL></li>
            </ul>
        </div>
    </div>

    <script>
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + type;
            resultDiv.innerHTML = message;
            resultDiv.style.display = 'block';
        }

        function testForgotPassword() {
            const email = document.getElementById('email').value;
            if (!email) {
                showResult('Please enter an email address', 'error');
                return;
            }

            showResult('Sending test request...', 'info');

            fetch('/test-email-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': getCSRFToken()
                },
                body: JSON.stringify({
                    email: email
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult('✅ Success: ' + data.message, 'success');
                } else {
                    showResult('❌ Error: ' + (data.message || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showResult('❌ Network Error: ' + error.message, 'error');
            });
        }

        function testOriginalForgotPassword() {
            const email = document.getElementById('email').value;
            if (!email) {
                showResult('Please enter an email address', 'error');
                return;
            }

            showResult('This would require reCAPTCHA. Use the test version instead.', 'info');
        }

        function getCSRFToken() {
            // Try to get CSRF token from meta tag or cookie
            const metaToken = document.querySelector('meta[name="csrf-token"]');
            if (metaToken) {
                return metaToken.getAttribute('content');
            }
            
            // Fallback: try to get from cookie
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'XSRF-TOKEN') {
                    return decodeURIComponent(value);
                }
            }
            
            return '';
        }

        // Add CSRF token meta tag if it doesn't exist
        if (!document.querySelector('meta[name="csrf-token"]')) {
            fetch('/forgot-password')
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const csrfMeta = doc.querySelector('meta[name="csrf-token"]');
                    if (csrfMeta) {
                        document.head.appendChild(csrfMeta);
                    }
                });
        }
    </script>
</body>
</html>
