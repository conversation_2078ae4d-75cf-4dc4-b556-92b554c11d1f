<?php

// Simple test script to check forgot password functionality
// Run this from the Laravel root directory: php test_forgot_password.php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Mail;
use App\Mail\SiteEmail;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Testing Forgot Password Functionality...\n\n";

// Test 1: Check mail configuration
echo "1. Checking mail configuration...\n";
$mailConfig = config('mail');
echo "Mail Driver: " . $mailConfig['default'] . "\n";
echo "SMTP Host: " . config('mail.mailers.smtp.host') . "\n";
echo "SMTP Port: " . config('mail.mailers.smtp.port') . "\n";
echo "From Address: " . config('mail.from.address') . "\n";

// Test 2: Check if user exists
echo "\n2. Checking if test user exists...\n";
try {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        echo "Test user found: " . $user->email . "\n";
    } else {
        echo "No test user found. Creating one...\n";
        $user = new \App\Models\User();
        $user->email = '<EMAIL>';
        $user->first_name = 'Test';
        $user->last_name = 'User';
        $user->username = 'testuser';
        $user->password = bcrypt('password');
        $user->save();
        echo "Test user created.\n";
    }
} catch (Exception $e) {
    echo "Error with user: " . $e->getMessage() . "\n";
}

// Test 3: Test simple email sending
echo "\n3. Testing simple email sending...\n";
try {
    $mail_data = (object)[
        'user' => $user,
        'subject' => 'Test Password Reset',
        'pre_header' => 'Test email',
        'content' => 'This is a test email for password reset functionality.',
    ];
    
    Mail::to($user->email)->send(new SiteEmail($mail_data));
    echo "Simple email sent successfully!\n";
} catch (Exception $e) {
    echo "Error sending simple email: " . $e->getMessage() . "\n";
}

// Test 4: Check database tables
echo "\n4. Checking database tables...\n";
try {
    $passwordResetTable = config('auth.passwords.users.table');
    echo "Password reset table: " . $passwordResetTable . "\n";
    
    $count = \DB::table($passwordResetTable)->count();
    echo "Current password reset records: " . $count . "\n";
} catch (Exception $e) {
    echo "Error checking database: " . $e->getMessage() . "\n";
}

// Test 5: Check recaptcha configuration
echo "\n5. Checking reCAPTCHA configuration...\n";
$recaptchaSiteKey = config('recaptchav3.sitekey');
$recaptchaSecret = config('recaptchav3.secret');
echo "reCAPTCHA Site Key: " . ($recaptchaSiteKey ? 'Set' : 'Not set') . "\n";
echo "reCAPTCHA Secret: " . ($recaptchaSecret ? 'Set' : 'Not set') . "\n";

echo "\nTest completed!\n";
