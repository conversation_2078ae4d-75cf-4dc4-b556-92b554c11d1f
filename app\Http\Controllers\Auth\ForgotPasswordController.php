<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\SendsPasswordResetEmails;
use App\Mail\ResetPassword;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Validator;
use Session;
use F9Web\ApiResponseHelpers;
use Illuminate\Http\JsonResponse;
use App\Models\AlertPreference;
use App\Models\Tracking;
use App\Models\UserGroupMap;
use App\Models\EmailTemplate;
use App\Mail\SiteEmail;

class ForgotPasswordController extends Controller
{
    use ApiResponseHelpers;

    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset emails and
    | includes a trait which assists in sending these notifications from
    | your application to your users. Feel free to explore this trait.
    |
    */

    use SendsPasswordResetEmails;

    public function __construct()
    {
        $this->middleware('guest');
    }

    // protected function validator(array $data)
    // {
    //     return Validator::make($data, [
    //         'email' => 'required|string|email|max:255|exists:users',
    //     ]);
    // }

    public function sendResetLinkEmail(Request $request): JsonResponse
    {
        try {
            \Log::info('Forgot Password Request Started', ['email' => $request->input('email')]);

            // Validate request
            $this->validate($request, [
                'email' => 'required|string|email|max:255|exists:users',
                'g-recaptcha-response' => 'required|recaptchav3:forgot_password,0.9',
            ], [
                'email.exists' => 'You are not authorized to reset password'
            ]);

            \Log::info('Validation passed');

            // Get user
            $user = User::where('email', $request->input('email'))->first();
            if (!$user) {
                \Log::error('User not found', ['email' => $request->input('email')]);
                return $this->respondError('User not found');
            }

            \Log::info('User found', ['user_id' => $user->id, 'email' => $user->email]);

            // Create password reset token
            $token = Password::getRepository()->createNewToken();
            \Log::info('Token created', ['token_length' => strlen($token)]);

            // Store token in database
            DB::table(config('auth.passwords.users.table'))->insert([
                'email' => $user->email,
                'token' => Hash::make($token),
                'created_at' => Carbon::now()
            ]);

            \Log::info('Token stored in database');

            $reset_password_link = url('/reset-password') . '?token=' . $token;
            \Log::info('Reset link created', ['link' => $reset_password_link]);

            // Try to send email using the complex template system first
            $emailSent = $this->sendTemplatedEmail($user, $reset_password_link);
            \Log::info('Template email attempt', ['success' => $emailSent]);

            // If template system fails, send simple email
            if (!$emailSent) {
                \Log::info('Attempting simple email');
                $simpleEmailSent = $this->sendSimpleResetEmail($user, $reset_password_link);
                \Log::info('Simple email attempt', ['success' => $simpleEmailSent]);
            }

            \Log::info('Forgot Password Request Completed Successfully');
            return $this->respondOk('Reset info sent to your email');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation Error: ' . json_encode($e->errors()));
            return $this->respondError('Validation failed: ' . implode(', ', array_flatten($e->errors())));
        } catch (\Exception $e) {
            \Log::error('Forgot Password Error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->respondError('An error occurred while sending reset email. Please try again.');
        }
    }

    private function sendTemplatedEmail($user, $reset_password_link)
    {
        try {
            \Log::info('Starting templated email', ['user_id' => $user->id]);

            // Check if user has alert preferences
            if (empty($user->alert_preferences)) {
                \Log::info('User has no alert preferences');
                return false;
            }

            $alert_preferences = @unserialize($user->alert_preferences);
            if (!$alert_preferences || !is_array($alert_preferences)) {
                \Log::info('Alert preferences invalid or not array');
                return false;
            }

            \Log::info('Alert preferences found', ['count' => count($alert_preferences)]);
            $emailSent = false;

            foreach ($alert_preferences as $key => $value) {
                $alert_preference = AlertPreference::where('key', $key)->first();
                if (!$alert_preference) continue;

                $user_email_templates = EmailTemplate::where('alert_preference_id', $alert_preference->id)
                    ->where('system_type', 'send_password_reset_link')
                    ->get();

                foreach ($user_email_templates as $user_email_template) {
                    try {
                        $email_body = @unserialize($user_email_template->email_body);
                        $sms_body = @unserialize($user_email_template->sms_body);

                        if (!$email_body) {
                            $email_body = $user_email_template->email_body;
                        }
                        if (!$sms_body) {
                            $sms_body = $user_email_template->sms_body;
                        }

                        $mail_data = (object)[
                            'user' => $user,
                            'reset_password_link' => $reset_password_link,
                            'subject' => $user_email_template->subject ?? 'Password Reset',
                            'pre_header' => $user_email_template->pre_header ?? 'Reset your password',
                            'email_body' => $email_body,
                            'sms_status' => $user_email_template->sms_status ? true : false,
                            'sms_body' => $sms_body,
                        ];

                        // Process email data
                        $processed_data = $this->sendEmailData($mail_data);
                        $mail_data->subject = $processed_data->subject ?? $mail_data->subject;
                        $mail_data->pre_header = $processed_data->pre_header ?? $mail_data->pre_header;
                        $mail_data->content = $processed_data->content ?? $mail_data->email_body;

                        // Create tracking record
                        $tracking = new Tracking();
                        $tracking->content = serialize($processed_data);
                        $tracking->from_email = config('mail.from.address');
                        $tracking->to_email = $user->email;
                        $tracking->subject = $mail_data->subject;
                        $tracking->lead_source = serialize(['source' => 'forgot_password']);
                        $tracking->ip = $this->getClientIpAddress();
                        $tracking->save();

                        // Send email
                        Mail::to($user->email)->send(new SiteEmail($mail_data));
                        $emailSent = true;

                    } catch (\Exception $e) {
                        \Log::error('Template email error: ' . $e->getMessage());
                        continue;
                    }
                }
            }

            return $emailSent;

        } catch (\Exception $e) {
            \Log::error('Templated email system error: ' . $e->getMessage());
            return false;
        }
    }

    private function sendSimpleResetEmail($user, $reset_password_link)
    {
        try {
            \Log::info('Starting simple email', ['user_email' => $user->email]);

            $mail_data = (object)[
                'user' => $user,
                'reset_password_link' => $reset_password_link,
                'subject' => 'Password Reset Request',
                'pre_header' => 'Reset your password',
                'content' => "Hello {$user->first_name},<br><br>You requested a password reset. Click the link below to reset your password:<br><br><a href='{$reset_password_link}'>Reset Password</a><br><br>If you didn't request this, please ignore this email.",
            ];

            \Log::info('Mail data prepared');

            // Create tracking record
            $tracking = new Tracking();
            $tracking->content = serialize($mail_data);
            $tracking->from_email = config('mail.from.address');
            $tracking->to_email = $user->email;
            $tracking->subject = $mail_data->subject;
            $tracking->lead_source = serialize(['source' => 'forgot_password_simple']);
            $tracking->ip = $this->getClientIpAddress();
            $tracking->save();

            \Log::info('Tracking record created', ['tracking_id' => $tracking->id]);

            Mail::to($user->email)->send(new SiteEmail($mail_data));
            \Log::info('Simple email sent successfully');
            return true;

        } catch (\Exception $e) {
            \Log::error('Simple email error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            // As last resort, use Laravel's built-in password reset
            try {
                \Log::info('Attempting fallback email');
                Mail::to($user->email)->send(new ResetPassword($reset_password_link, $user->email));
                \Log::info('Fallback email sent successfully');
                return true;
            } catch (\Exception $e2) {
                \Log::error('Fallback email error: ' . $e2->getMessage(), [
                    'file' => $e2->getFile(),
                    'line' => $e2->getLine()
                ]);
                return false;
            }
        }
    }

    public function afterSendResetLink()
    {
        $email = Session::get('email');
        if (!$email) return redirect()->with('success', 'your session expire');
        // if (!$email) return redirect()->to('/');
        // $page = (object) [
        //     'slug' => 'thank-you-reset',
        //     'meta_title' => 'Welcome',
        //     'title' => 'Reset Password',
        //     'content' => 'Password verification link send to this email ' . $email,
        // ];
        // $page->top_menu = $this->getMenu('top_menu', $page->slug);
        // $page->menu = $this->getMenu('main_menu', $page->slug);
        // return view('web.thanks', compact('page'));

        return redirect()->with('success', 'afterSendResetLink');
    }

    // Test method without reCAPTCHA for debugging
    public function testSendResetLinkEmail(Request $request): JsonResponse
    {
        try {
            \Log::info('Test Forgot Password Request Started', ['email' => $request->input('email')]);

            // Validate request (without reCAPTCHA)
            $this->validate($request, [
                'email' => 'required|string|email|max:255|exists:users',
            ], [
                'email.exists' => 'You are not authorized to reset password'
            ]);

            \Log::info('Test validation passed');

            // Get user
            $user = User::where('email', $request->input('email'))->first();
            if (!$user) {
                \Log::error('User not found', ['email' => $request->input('email')]);
                return $this->respondError('User not found');
            }

            \Log::info('User found', ['user_id' => $user->id, 'email' => $user->email]);

            // Create password reset token
            $token = Password::getRepository()->createNewToken();
            \Log::info('Token created', ['token_length' => strlen($token)]);

            // Store token in database
            DB::table(config('auth.passwords.users.table'))->insert([
                'email' => $user->email,
                'token' => Hash::make($token),
                'created_at' => Carbon::now()
            ]);

            \Log::info('Token stored in database');

            $reset_password_link = url('/reset-password') . '?token=' . $token;
            \Log::info('Reset link created', ['link' => $reset_password_link]);

            // Send simple email directly
            $emailSent = $this->sendSimpleResetEmail($user, $reset_password_link);
            \Log::info('Simple email attempt', ['success' => $emailSent]);

            if ($emailSent) {
                \Log::info('Test Forgot Password Request Completed Successfully');
                return $this->respondOk('Reset info sent to your email');
            } else {
                return $this->respondError('Failed to send email');
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Test Validation Error: ' . json_encode($e->errors()));
            return $this->respondError('Validation failed: ' . implode(', ', array_flatten($e->errors())));
        } catch (\Exception $e) {
            \Log::error('Test Forgot Password Error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->respondError('An error occurred while sending reset email. Please try again.');
        }
    }
}
